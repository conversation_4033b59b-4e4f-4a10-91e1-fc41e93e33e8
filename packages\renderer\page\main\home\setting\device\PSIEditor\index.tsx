import {
  computed,
  defineComponent,
  onMounted,
  reactive,
  ref,
  toRaw,
  toRef,
  useModel,
  watch
} from 'vue'
import MyDialog from '@/renderer/components/dialog/index.vue'
import { useBem, useBizEngine, useBizMain, useHandler, useTableCommonMenu } from '@/renderer/hooks'
import $styles from './index.module.scss'
import TableTool, { isAddOrInsertType, OpType } from '@/renderer/components/TableTool'
import { SetupDeviceRow } from '../type.ts'
import DynamicDIalog from '@/renderer/components/DynamicDIalog/index.tsx'
import { DEVICE_CONFIGS } from './configs'
import type { NestedDialogContext } from '@/renderer/components/DynamicDIalog/types'
import { convertType } from '@/renderer/utils/common.ts'
import { getConfigData } from '@/renderer/components/DynamicDIalog/utils.ts'
import { BizEngine, BizMain } from '@/renderer/logic/index.ts'
import { ArincCardCfg, hardwareTypes, VxiCard, vxiCardType, VxiInfo } from '@wuk/cfg'

export default defineComponent({
  name: 'VXIEditor',
  props: {
    showVXIEditor: {
      type: Boolean,
      default: false
    },
    currentDevice: {
      type: Object,
      default: () => {}
    }
  },
  emits: ['update:showVXIEditor'],
  setup(props) {
    const mainPtr = useBizMain()
    const showVXIEditor = useModel(props, 'showVXIEditor')
    let originList = [] as SetupDeviceRow[]
    const { b, e, m } = useBem('vxi-editor', $styles)
    const currentRow = ref()
    const model = reactive({
      cards: [] as SetupDeviceRow[]
    })
    const createRow = (row_type: SetupDeviceRow['row_type']) => ({
      name: props.currentDevice.name,
      slot: 10,
      card_type: '',
      card_address: '',
      no_hardware: false,
      flag: true,
      row_type
    })
    const { handleRowMenu } = useTableCommonMenu(
      toRef(model, 'cards'),
      async (key, ...args) => {
        const { row, rowIndex } = args[0]
        currentRow.value = { ...row, rowIndex }
        switch (key) {
          case 'addKey':
            model.cards.push(createRow('add'))
            break
          case 'deleteKey':
            handleOp('delete', row, rowIndex)
            break
          case 'modifyKey':
            handleOp('edit', row, rowIndex)
            break
          case 'insertKey':
            model.cards.splice(rowIndex + 1, 0, createRow('insert'))
            break
          case 'deviceEditor':
            if (!currentRow.value?.card_type) return
            dialogConfig.value = await dialogComputed()
        }
      },
      [1],
      [
        { key: 'addKey', label: 'add' },
        { key: 'insertKey', label: 'insert' },
        { key: 'modifyKey', label: 'modify' },
        { key: 'deleteKey', label: 'delete' },
        { key: 'deviceEditor', label: 'Device Editor' }
      ]
    )
    const handleOp = (op: OpType, row: SetupDeviceRow | undefined, index: number) => {
      if (!row) return
      switch (op) {
        case 'edit':
          row.flag = true
          break
        case 'delete':
          handleDelete(index, row)
          break
        case 'select':
          handleSelect(row, index)
          row.row_type = '*'
          row.flag = false
          break
        case 'cancel':
          handleOpCancel(row, index)
          break
      }
    }
    const handleOpCancel = (row: SetupDeviceRow, index: number) => {
      if (isAddOrInsertType(row.row_type)) {
        model.cards.splice(index, 1)
        return
      }
      model.cards.splice(index, 1, { ...originList[index], flag: false, row_type: '*' })
    }
    const handleSelect = async (row: SetupDeviceRow, index: number) => {
      let res: boolean | undefined = undefined
      res = await mainPtr.value?.saveHardware<VxiInfo>(row.name, { cards: toRaw(model.cards) })
      if (!res) return
      originList = JSON.parse(JSON.stringify(model.cards))
    }
    const handleDelete = async (index: number, row: SetupDeviceRow) => {
      await mainPtr.value?.saveHardware<VxiInfo>(row.name, { cards: model.cards })
      model.cards.splice(index, 1)
    }
    const handleOk = async () => {
      showVXIEditor.value = false
    }

    const cardOption = Object.entries(vxiCardType).map(([key, value]) => ({
      label: value,
      value: key
    }))

    const testModeOption = [
      {
        label: 'With Hardware',
        value: true
      },
      {
        label: 'No Hardware',
        value: false
      }
    ]

    const dynamicShow = ref(false)

    const dialogConfig = ref()

    const dialogComputed = async () => {
      if (!currentRow.value?.card_type) return undefined

      const deviceType = currentRow.value.card_type

      // 传入自己所需要的上下文数据
      const context: NestedDialogContext = {
        currentDevice: props.currentDevice,
        mainData: currentRow.value
      }

      return getConfigData(deviceType, DEVICE_CONFIGS, context)
    }

    watch(
      () => dialogConfig.value,
      newValue => {
        if (newValue) dynamicShow.value = true
      }
    )

    // API回调处理函数
    const createApiCallbacks = () => {
      return {
        onDataSave: async (allData: any, context?: NestedDialogContext) => {
          const formData = allData?.formData
          if (context?.key === 'CENCO_TACH_1') {
            const parentTableData = context?.parentTableData
            const selectedRowIndex = context?.parentRowIndex

            if (parentTableData && selectedRowIndex !== undefined) {
              const targetRow = parentTableData[selectedRowIndex]
              if (targetRow) {
                targetRow.signalConfig = {
                  conversionType: formData.conversionType,
                  signal_range: formData.signal_range,
                  scale_range: formData.scale_range,
                  low_pass_filter: formData.low_pass_filter,
                  sensitivity: formData.sensitivity,
                  comments: formData.comments,
                  measurement: formData.measurement
                }

                const mainData = context?.mainData
                if (mainData) {
                  const card = model.cards[mainData.rowIndex]
                  if (card?.cfg?.table && selectedRowIndex < card.cfg.table.length) {
                    card.cfg.table[selectedRowIndex] = targetRow
                  }
                  return await mainPtr.value?.saveHardware<VxiInfo>(mainData.name, {
                    cards: toRaw(model.cards)
                  })
                }
              }
            }
          }

          if (context?.key === 'CENCO_TACH_2') {
            const nestedPath = context?.nestedPath
            if (nestedPath && nestedPath.length >= 2) {
              const signalEditorPath = nestedPath[nestedPath.length - 2]
              const parentFormData = signalEditorPath?.formData
              const selectedRowIndex = nestedPath[0].selectedRowIndex

              if (parentFormData && selectedRowIndex !== undefined) {
                const finalData = {
                  ...parentFormData,
                  coefficient0: formData.coefficient0
                }
                const mainData = context?.mainData
                if (!mainData) return
                const card = model.cards[mainData.rowIndex]
                if (card?.cfg?.table && selectedRowIndex < card.cfg.table.length) {
                  card.cfg.table[selectedRowIndex].signalConfig = finalData
                }

                return await mainPtr.value?.saveHardware<VxiInfo>(mainData.name, {
                  cards: toRaw(model.cards)
                })
              }
            }
          }

          if (context?.key === 'ARINC_429') {
            const arincInfo: ArincCardCfg = {
              name: context?.mainData?.name,
              address: context?.mainData?.card_address,
              clock_tic: allData.formData.clock_tic,
              nips: 4,
              index: 0,
              slot: context?.mainData?.slot || 0,
              transmitters: [],
              receivers: []
            }

            allData.tableData.forEach((item: any) => {
              if (item.type === 'transmitter') {
                arincInfo.transmitters.push(item)
              } else {
                arincInfo.receivers.push(item)
              }
            })

            model.cards[context?.mainData.rowIndex].cfg = {
              ...arincInfo
            }

            return await mainPtr.value?.saveCardCfg<ArincCardCfg>(
              context?.mainData.name,
              context?.mainData.card_type.toLowerCase(),
              context?.mainData.card_address,
              arincInfo
            )
          }
          return false
        }
      }
    }

    const getDataInfo = async () => {
      const { cards = [] } = ((await mainPtr.value?.loadHardware(props.currentDevice.name)) ||
        []) as VxiInfo<VxiCard>

      model.cards = cards.map((item: any) => {
        const flag = false
        const row_type = '*'
        return {
          ...item,
          flag,
          row_type,
          card_type: item.card_type?.toUpperCase()
        }
      })
      originList = JSON.parse(JSON.stringify(model.cards))
    }

    onMounted(async () => {
      await getDataInfo()
    })

    useHandler(mainPtr, BizMain.OnHardwareChanged, getDataInfo)

    return () => {
      return (
        <>
          <MyDialog
            width='800px'
            v-model={showVXIEditor.value}
            title={props.currentDevice.name + ' VXI Editor'}
            onOk={handleOk}>
            <div class={[e('body'), 'cfg-setup']}>
              <div class={['cfg-setup_table', e('body', 'table')]}>
                <wui-table
                  onRow-contextmenu={handleRowMenu}
                  border
                  height='100%'
                  data={model.cards}>
                  {{
                    default: () => (
                      <>
                        <wui-table-column prop='slot' label='Slot #' align='center'>
                          {{
                            default: ({ row }: any) => (
                              <>
                                {row.flag ? (
                                  <wui-input-number
                                    v-model={row.slot}
                                    input-align='left'
                                    clearable
                                    controls={false}
                                    placeholder='Please Input'
                                  />
                                ) : (
                                  row.slot
                                )}
                              </>
                            )
                          }}
                        </wui-table-column>
                        <wui-table-column prop='card_type' label='Card' align='center'>
                          {{
                            default: ({ row }: any) => (
                              <>
                                {row.flag ? (
                                  <wui-select
                                    v-model={row.card_type}
                                    placeholder='Select'
                                    filterable>
                                    {cardOption.map(({ label, value }, index) => (
                                      <wui-option key={index} label={label} value={value} />
                                    ))}
                                  </wui-select>
                                ) : (
                                  convertType(cardOption, row.card_type)
                                )}
                              </>
                            )
                          }}
                        </wui-table-column>
                        <wui-table-column prop='card_address' label='Address' align='center'>
                          {{
                            default: ({ row }: any) => (
                              <>
                                {row.flag ? (
                                  <wui-input
                                    v-model={row.card_address}
                                    placeholder='Please Input'
                                  />
                                ) : (
                                  row.card_address
                                )}
                              </>
                            )
                          }}
                        </wui-table-column>
                        <wui-table-column prop='no_hardware' label='Test Mode' align='center'>
                          {{
                            default: ({ row }: any) => (
                              <>
                                {row.flag ? (
                                  <wui-select v-model={row.no_hardware} placeholder='Select'>
                                    {testModeOption.map(({ label, value }, index) => (
                                      <wui-option key={index} label={label} value={value} />
                                    ))}
                                  </wui-select>
                                ) : (
                                  convertType(testModeOption, row.no_hardware)
                                )}
                              </>
                            )
                          }}
                        </wui-table-column>
                        <wui-table-column width='100px' align='center'>
                          {{
                            default: ({ row, $index }: any) => (
                              <TableTool.Op
                                flag={row.flag}
                                onOp={op => handleOp(op, row, $index)}
                              />
                            )
                          }}
                        </wui-table-column>
                      </>
                    ),
                    empty: () => <TableTool.Empty />
                  }}
                </wui-table>
              </div>
            </div>
          </MyDialog>
          {dynamicShow.value && (
            <DynamicDIalog
              v-model:visible={dynamicShow.value}
              config={dialogConfig.value}
              apiCallbacks={createApiCallbacks()}
            />
          )}
        </>
      )
    }
  }
})
