import {
  computed,
  defineComponent,
  onMounted,
  reactive,
  ref,
  toRaw,
  toRef,
  useModel,
  watch
} from 'vue'
import MyDialog from '@/renderer/components/dialog/index.vue'
import { useBem, useBizMain, useHandler, useTableCommonMenu } from '@/renderer/hooks'
import $styles from './index.module.scss'
import TableTool, { isAddOrInsertType, OpType, RowType } from '@/renderer/components/TableTool'
import { convertType } from '@/renderer/utils/common.ts'
import { BizMain } from '@/renderer/logic/index.ts'

// PSI Device 数据类型
interface PSIDeviceRow {
  name: string
  type: string
  engine_specific: string
  test_mode: string
  flag: boolean
  row_type: '*' | 'add' | 'insert'
}

// Calibrator 数据类型
interface CalibratorRow {
  name: string
  type: string
  range: string
  address: string
  flag: boolean
  row_type: '*' | 'add' | 'insert'
}

export default defineComponent({
  name: 'PSIEditor',
  props: {
    showPSIEditor: {
      type: Boolean,
      default: false
    },
    currentDevice: {
      type: Object,
      default: () => {}
    }
  },
  emits: ['update:showPSIEditor'],
  setup(props) {
    const mainPtr = useBizMain()
    const showPSIEditor = useModel(props, 'showPSIEditor')
    let originPSIList = [] as PSIDeviceRow[]
    let originCalibratorList = [] as CalibratorRow[]
    const { b, e, m } = useBem('psi-editor', $styles)
    const currentRow = ref()
    const model = reactive({
      psis: [] as PSIDeviceRow[],
      calibs: [] as CalibratorRow[]
    })
    const createPSIRow = (row_type: '*' | 'add' | 'insert'): PSIDeviceRow => ({
      name: `psi${model.psis.length + 1}`,
      type: 'PSI0016',
      engine_specific: 'PLCENG',
      test_mode: 'st With Device Hardware',
      flag: true,
      row_type
    })

    const createCalibratorRow = (row_type: '*' | 'add' | 'insert'): CalibratorRow => ({
      name: `psi${model.calibs.length + 1}`,
      type: 'PSI0034',
      range: '100',
      address: '1',
      flag: true,
      row_type
    })

    // PSI表格的右键菜单处理
    const { handleRowMenu: handlePSIRowMenu } = useTableCommonMenu(
      toRef(model, 'psis'),
      async (key, ...args) => {
        const { row, rowIndex } = args[0]
        currentRow.value = { ...row, rowIndex, tableType: 'psi' }
        switch (key) {
          case 'addKey':
            model.psis.push(createPSIRow('add'))
            break
          case 'deleteKey':
            handlePSIOp('delete', row, rowIndex)
            break
          case 'modifyKey':
            handlePSIOp('edit', row, rowIndex)
            break
          case 'insertKey':
            model.psis.splice(rowIndex + 1, 0, createPSIRow('insert'))
            break
        }
      },
      [1],
      [
        { key: 'addKey', label: 'add' },
        { key: 'insertKey', label: 'insert' },
        { key: 'modifyKey', label: 'modify' },
        { key: 'deleteKey', label: 'delete' }
      ]
    )

    // Calibrator表格的右键菜单处理
    const { handleRowMenu: handleCalibratorRowMenu } = useTableCommonMenu(
      toRef(model, 'calibs'),
      async (key, ...args) => {
        const { row, rowIndex } = args[0]
        currentRow.value = { ...row, rowIndex, tableType: 'calibrator' }
        switch (key) {
          case 'addKey':
            model.calibs.push(createCalibratorRow('add'))
            break
          case 'deleteKey':
            handleCalibratorOp('delete', row, rowIndex)
            break
          case 'modifyKey':
            handleCalibratorOp('edit', row, rowIndex)
            break
          case 'insertKey':
            model.calibs.splice(rowIndex + 1, 0, createCalibratorRow('insert'))
            break
        }
      },
      [1],
      [
        { key: 'addKey', label: 'add' },
        { key: 'insertKey', label: 'insert' },
        { key: 'modifyKey', label: 'modify' },
        { key: 'deleteKey', label: 'delete' }
      ]
    )
    // PSI表格操作处理
    const handlePSIOp = (op: OpType, row: PSIDeviceRow | undefined, index: number) => {
      if (!row) return
      switch (op) {
        case 'edit':
          row.flag = true
          break
        case 'delete':
          handlePSIDelete(index, row)
          break
        case 'select':
          handlePSISelect(row, index)
          row.row_type = '*'
          row.flag = false
          break
        case 'cancel':
          handlePSIOpCancel(row, index)
          break
      }
    }

    const handlePSIOpCancel = (row: PSIDeviceRow, index: number) => {
      if (isAddOrInsertType(row.row_type)) {
        model.psis.splice(index, 1)
        return
      }
      model.psis.splice(index, 1, { ...originPSIList[index], flag: false, row_type: '*' })
    }

    const handlePSISelect = async (row: PSIDeviceRow, index: number) => {
      // 这里可以添加保存PSI数据的逻辑
      row.row_type = '*'
      row.flag = false
      originPSIList = JSON.parse(JSON.stringify(model.psis))
    }

    const handlePSIDelete = async (index: number, row: PSIDeviceRow) => {
      // 这里可以添加删除PSI数据的逻辑
      model.psis.splice(index, 1)
    }

    // Calibrator表格操作处理
    const handleCalibratorOp = (op: OpType, row: CalibratorRow | undefined, index: number) => {
      if (!row) return
      switch (op) {
        case 'edit':
          row.flag = true
          break
        case 'delete':
          handleCalibratorDelete(index, row)
          break
        case 'select':
          handleCalibratorSelect(row, index)
          row.row_type = '*'
          row.flag = false
          break
        case 'cancel':
          handleCalibratorOpCancel(row, index)
          break
      }
    }

    const handleCalibratorOpCancel = (row: CalibratorRow, index: number) => {
      if (isAddOrInsertType(row.row_type)) {
        model.calibs.splice(index, 1)
        return
      }
      model.calibs.splice(index, 1, { ...originCalibratorList[index], flag: false, row_type: '*' })
    }

    const handleCalibratorSelect = async (row: CalibratorRow, index: number) => {
      // 这里可以添加保存Calibrator数据的逻辑
      row.row_type = '*'
      row.flag = false
      originCalibratorList = JSON.parse(JSON.stringify(model.calibs))
    }

    const handleCalibratorDelete = async (index: number, row: CalibratorRow) => {
      // 这里可以添加删除Calibrator数据的逻辑
      model.calibs.splice(index, 1)
    }
    const handleOk = async () => {
      showPSIEditor.value = false
    }

    // PSI设备类型选项
    const psiTypeOptions = [
      { label: 'PSI0016', value: 'PSI0016' },
      { label: 'PSI0021', value: 'PSI0021' }
    ]

    // 引擎特定选项
    const engineSpecificOptions = [
      { label: 'PLCENG', value: 'PLCENG' },
      { label: 'ALL', value: 'ALL' }
    ]

    // 测试模式选项
    const testModeOptions = [
      { label: 'st With Device Hardware', value: 'st With Device Hardware' },
      { label: 'st Without Device Hardware', value: 'st Without Device Hardware' }
    ]

    // Calibrator类型选项
    const calibratorTypeOptions = [{ label: 'PSI0034', value: 'PSI0034' }]

    const getDataInfo = async () => {
      // 初始化PSI设备数据
      model.psis = [
        {
          name: 'psi2',
          type: 'PSI0016',
          engine_specific: 'PLCENG',
          test_mode: 'st With Device Hardware',
          flag: false,
          row_type: '*'
        },
        {
          name: 'psi1',
          type: 'PSI0021',
          engine_specific: 'ALL',
          test_mode: 'st With Device Hardware',
          flag: false,
          row_type: '*'
        }
      ]

      // 初始化Calibrator数据
      model.calibs = [
        {
          name: 'psi',
          type: 'PSI0034',
          range: '100',
          address: '1',
          flag: false,
          row_type: '*'
        }
      ]

      originPSIList = JSON.parse(JSON.stringify(model.psis))
      originCalibratorList = JSON.parse(JSON.stringify(model.calibs))
    }

    onMounted(async () => {
      await getDataInfo()
    })

    useHandler(mainPtr, BizMain.OnHardwareChanged, getDataInfo)

    return () => {
      return (
        <MyDialog
          width='900px'
          v-model={showPSIEditor.value}
          title='PSI5001 Device List Editor'
          onOk={handleOk}>
          <div class={[e('body'), 'cfg-setup']}>
            {/* PSI Device Editor 表格 */}
            <div style={{ marginBottom: '20px' }}>
              <div
                style={{
                  backgroundColor: '#6B5B95',
                  color: 'white',
                  padding: '8px 16px',
                  fontSize: '14px',
                  fontWeight: 'bold',
                  textAlign: 'center'
                }}>
                PSI Device Editor
              </div>
              <div class={['cfg-setup_table', e('body', 'table')]}>
                <wui-table
                  onRow-contextmenu={handlePSIRowMenu}
                  border
                  height='200px'
                  data={model.psis}>
                  {{
                    default: () => (
                      <>
                        <wui-table-column
                          prop='name'
                          label='PSI Node Name'
                          align='center'
                          width='200px'>
                          {{
                            default: ({ row }: any) => (
                              <>
                                {row.flag ? (
                                  <wui-input v-model={row.name} placeholder='Please Input' />
                                ) : (
                                  row.name
                                )}
                              </>
                            )
                          }}
                        </wui-table-column>
                        <wui-table-column prop='type' label='Type' align='center' width='150px'>
                          {{
                            default: ({ row }: any) => (
                              <>
                                {row.flag ? (
                                  <wui-select v-model={row.type} placeholder='Select'>
                                    {psiTypeOptions.map(({ label, value }, index) => (
                                      <wui-option key={index} label={label} value={value} />
                                    ))}
                                  </wui-select>
                                ) : (
                                  convertType(psiTypeOptions, row.type)
                                )}
                              </>
                            )
                          }}
                        </wui-table-column>
                        <wui-table-column
                          prop='engine_specific'
                          label='Engine Specific'
                          align='center'
                          width='150px'>
                          {{
                            default: ({ row }: any) => (
                              <>
                                {row.flag ? (
                                  <wui-select v-model={row.engine_specific} placeholder='Select'>
                                    {engineSpecificOptions.map(({ label, value }, index) => (
                                      <wui-option key={index} label={label} value={value} />
                                    ))}
                                  </wui-select>
                                ) : (
                                  convertType(engineSpecificOptions, row.engine_specific)
                                )}
                              </>
                            )
                          }}
                        </wui-table-column>
                        <wui-table-column prop='test_mode' label='Test Mode' align='center'>
                          {{
                            default: ({ row }: any) => (
                              <>
                                {row.flag ? (
                                  <wui-select v-model={row.test_mode} placeholder='Select'>
                                    {testModeOptions.map(({ label, value }, index) => (
                                      <wui-option key={index} label={label} value={value} />
                                    ))}
                                  </wui-select>
                                ) : (
                                  convertType(testModeOptions, row.test_mode)
                                )}
                              </>
                            )
                          }}
                        </wui-table-column>
                        <wui-table-column width='100px' align='center'>
                          {{
                            default: ({ row, $index }: any) => (
                              <TableTool.Op
                                flag={row.flag}
                                onOp={op => handlePSIOp(op, row, $index)}
                              />
                            )
                          }}
                        </wui-table-column>
                      </>
                    ),
                    empty: () => <TableTool.Empty />
                  }}
                </wui-table>
              </div>
            </div>

            {/* Calibrator 表格 */}
            <div>
              <div class={['cfg-setup_table', e('body', 'table')]}>
                <wui-table
                  onRow-contextmenu={handleCalibratorRowMenu}
                  border
                  height='200px'
                  data={model.calibs}>
                  {{
                    default: () => (
                      <>
                        <wui-table-column
                          prop='name'
                          label='Calibrator Node Name'
                          align='center'
                          width='200px'>
                          {{
                            default: ({ row }: any) => (
                              <>
                                {row.flag ? (
                                  <wui-input v-model={row.name} placeholder='Please Input' />
                                ) : (
                                  row.name
                                )}
                              </>
                            )
                          }}
                        </wui-table-column>
                        <wui-table-column prop='type' label='Type' align='center' width='150px'>
                          {{
                            default: ({ row }: any) => (
                              <>
                                {row.flag ? (
                                  <wui-select v-model={row.type} placeholder='Select'>
                                    {calibratorTypeOptions.map(({ label, value }, index) => (
                                      <wui-option key={index} label={label} value={value} />
                                    ))}
                                  </wui-select>
                                ) : (
                                  convertType(calibratorTypeOptions, row.type)
                                )}
                              </>
                            )
                          }}
                        </wui-table-column>
                        <wui-table-column prop='range' label='Range' align='center' width='150px'>
                          {{
                            default: ({ row }: any) => (
                              <>
                                {row.flag ? (
                                  <wui-input v-model={row.range} placeholder='Please Input' />
                                ) : (
                                  row.range
                                )}
                              </>
                            )
                          }}
                        </wui-table-column>
                        <wui-table-column prop='address' label='Address' align='center'>
                          {{
                            default: ({ row }: any) => (
                              <>
                                {row.flag ? (
                                  <wui-input v-model={row.address} placeholder='Please Input' />
                                ) : (
                                  row.address
                                )}
                              </>
                            )
                          }}
                        </wui-table-column>
                        <wui-table-column width='100px' align='center'>
                          {{
                            default: ({ row, $index }: any) => (
                              <TableTool.Op
                                flag={row.flag}
                                onOp={op => handleCalibratorOp(op, row, $index)}
                              />
                            )
                          }}
                        </wui-table-column>
                      </>
                    ),
                    empty: () => <TableTool.Empty />
                  }}
                </wui-table>
              </div>
            </div>
          </div>
        </MyDialog>
      )
    }
  }
})
